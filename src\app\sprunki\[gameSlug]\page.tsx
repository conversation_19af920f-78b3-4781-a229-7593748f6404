import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { notFound } from 'next/navigation';
import { getGameBySlug, getTrendingGames, getNewGames } from '@/lib/api';
import { Metadata, ResolvingMetadata } from 'next';
import Script from 'next/script';
import GamePlayerWrapper from '../../components/GamePlayerWrapper';
import TwikooComments from '../../components/TwikooComments';
import ClientSideMarkdown from './ClientSideMarkdown';
import '../../styles/twikoo-custom.css';

// Configure Edge Runtime to support Cloudflare Pages
export const runtime = 'edge';

// Dynamically generate metadata
export async function generateMetadata(
  { params }: { params: Promise<{ gameSlug: string }> }
): Promise<Metadata> {
  try {
    // Get game data
    const { gameSlug } = await params;
    const game = await getGameBySlug(gameSlug);

    if (!game) {
      return {
        title: 'Game Not Found',
        description: 'The requested game could not be found.'
      };
    }

    // Get category info to build correct URL
    const category = game.free_games_online_categories;
    const categorySlug = category?.slug || 'sprunki';
    const canonicalUrl = `${process.env.NEXT_PUBLIC_SITE_URL || 'https://free-gamesonline.com'}/${categorySlug}/${gameSlug}`;

    return {
      title: `${game.title} | free-gamesonline.com`,
      description: game.short_description || 'Play this amazing game for free online',
      keywords: '', // Explicitly set to empty, don't use layout keywords
      robots: {
        index: true,
        follow: true,
        nosnippet: true,
        'max-snippet': -1,
        'max-image-preview': 'large',
        'max-video-preview': -1,
      },
      alternates: {
        canonical: canonicalUrl,
      },
      openGraph: {
        title: `${game.title} | free-gamesonline.com`,
        description: game.short_description || 'Play this amazing game for free online',
        images: game.cover_image ? [game.cover_image] : [],
        url: canonicalUrl,
      },
      twitter: {
        card: 'summary_large_image',
        title: `${game.title} | free-gamesonline.com`,
        description: game.short_description || 'Play this amazing game for free online',
        images: game.cover_image ? [game.cover_image] : [],
      }
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Game Not Found',
      description: 'The requested game could not be found.'
    };
  }
}

// Game details page component
export default async function GamePage({ params }: { params: Promise<{ gameSlug: string }> }) {
  try {
    // Get game data
    const { gameSlug } = await params;
    
    // Check environment variables
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Missing Supabase environment variables');
      notFound();
    }
    
    const game = await getGameBySlug(gameSlug);

    // If game does not exist or is not published, return 404
    if (!game || game.status !== 'published') {
      notFound();
    }

    // Extract category info from game data
    const category = game.free_games_online_categories;

    // Extract tag info from game data
    const tags = game.free_games_online_game_tags?.map((tag: any) => tag.free_games_online_tags) || [];

    // Fetch related game data in parallel for better performance, add error handling
    const [trendingGames, newGames] = await Promise.all([
      getTrendingGames(game.id, 4).catch(err => {
        console.error('Error fetching trending games:', err);
        return [];
      }),
      getNewGames(game.id, 4).catch(err => {
        console.error('Error fetching new games:', err);
        return [];
      })
    ]);

    // Get the full URL of the current page
    const categorySlug = category?.slug || 'sprunki';
    const currentUrl = `${process.env.NEXT_PUBLIC_SITE_URL || 'https://free-gamesonline.com'}/${categorySlug}/${gameSlug}`;

    // Generate structured data
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "VideoGame",
      "name": game.title,
      "description": game.short_description,
      "image": game.cover_image,
      "url": currentUrl,
      "genre": category?.name || "Game",
      "publisher": {
        "@type": "Organization",
        "name": "Free Games Online"
      },
      "datePublished": game.created_at,
      "applicationCategory": "Game",
      "operatingSystem": "Web Browser",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
      }
    };

    return (
      <>
        <Script
          id="structured-data"
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData)
          }}
        />
        <Script
          src="https://platform-api.sharethis.com/js/sharethis.js#property=yoursharethisproperty&product=inline-share-buttons"
          strategy="lazyOnload"
        />
        <main className="min-h-screen bg-gradient-to-b from-purple-900/40 via-black to-black text-white">
        <div className="container mx-auto px-4 py-12">
          {/* Breadcrumb navigation */}
          <nav className="flex items-center text-base mb-8">
            <Link href="/" className="text-white hover:text-purple-300 font-medium transition-colors">Home</Link>
            <span className="mx-2 text-white">/</span>
            {category && (
              <>
                <Link
                  href={`/${category.slug}`}
                  className="text-white hover:text-purple-300 font-medium transition-colors"
                >
                  {category.name}
                </Link>
                <span className="mx-2 text-white">/</span>
              </>
            )}
            <span className="text-white font-medium">{game.title}</span>
          </nav>

          {/* Game title and description */}
          <div className="mb-8">
            <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-pink-400 via-purple-400 to-pink-500 text-transparent bg-clip-text drop-shadow-lg">
              {game.title}
            </h1>
            <p className="text-lg text-gray-300 max-w-3xl">
              {game.short_description}
            </p>
          </div>

          {/* Game tags */}
          {tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-8">
              {tags.map((tag: any) => (
                <Link
                  href={`/tag/${tag.slug}`}
                  key={tag.id}
                  className="px-3 py-1 bg-purple-900/30 hover:bg-purple-800/40 text-white text-sm rounded-full transition-colors"
                >
                  {tag.name}
                </Link>
              ))}
            </div>
          )}

          {/* Main layout with game content and related games */}
          <div className="flex flex-col lg:flex-row gap-6 mb-16">
            {/* Left side - Game content area */}
            <div className="lg:w-5/6">
              {/* Game embed - client component */}
              <GamePlayerWrapper
                embedUrl={game.embed_code || ''}
                coverImage={game.cover_image}
                title={game.title}
              />

              {/* Share Buttons */}
              <div className="max-w-2xl mx-auto mb-16">
                <div className="sharethis-inline-share-buttons"></div>
              </div>

              {/* New games area - horizontal layout */}
              {newGames.length > 0 && (
                <div className="mb-16">
                  <h2 className="text-2xl font-bold mb-6 bg-gradient-to-r from-pink-400 to-purple-400 text-transparent bg-clip-text">
                    New Games
                  </h2>
                  <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-3">
                    {newGames.map((newGame) => (
                      <Link
                        key={newGame.id}
                        href={`/${category?.slug || 'sprunki'}/${newGame.slug}`}
                        className="block bg-gray-900 rounded-lg overflow-hidden border border-purple-600/30 hover:border-purple-600 transition-all transform hover:scale-105"
                      >
                        <div className="aspect-square relative">
                          <Image
                            src={newGame.cover_image}
                            alt={newGame.title}
                            fill
                            className="object-cover"
                            sizes="(max-width: 640px) 33vw, (max-width: 768px) 25vw, 16vw"
                          />
                        </div>
                        <div className="p-1 text-center">
                          <h3 className="text-xs font-medium text-white hover:text-purple-400 transition-colors line-clamp-1">
                            {newGame.title}
                          </h3>
                        </div>
                      </Link>
                    ))}
                  </div>
                  <div className="text-center mt-4">
                    <Link href="/" className="text-purple-400 hover:text-purple-300 transition-colors">
                      Explore More Games
                    </Link>
                  </div>
                </div>
              )}

              {/* Trending games area - horizontal layout (mobile only) */}
              {trendingGames.length > 0 && (
                <div className="mb-16 lg:hidden">
                  <h2 className="text-2xl font-bold mb-6 bg-gradient-to-r from-pink-400 to-purple-400 text-transparent bg-clip-text">
                    Trending Games
                  </h2>
                  <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-3">
                    {trendingGames.map((trendingGame) => (
                      <Link
                        key={trendingGame.id}
                        href={`/${category?.slug || 'sprunki'}/${trendingGame.slug}`}
                        className="block bg-gray-900 rounded-lg overflow-hidden border border-purple-600/30 hover:border-purple-600 transition-all transform hover:scale-105"
                      >
                        <div className="aspect-square relative">
                          <Image
                            src={trendingGame.cover_image}
                            alt={trendingGame.title}
                            fill
                            className="object-cover"
                            sizes="(max-width: 640px) 33vw, (max-width: 768px) 25vw, 16vw"
                          />
                        </div>
                        <div className="p-1 text-center">
                          <h3 className="text-xs font-medium text-white hover:text-purple-400 transition-colors line-clamp-1">
                            {trendingGame.title}
                          </h3>
                        </div>
                      </Link>
                    ))}
                  </div>
                  {category && (
                    <div className="text-center mt-4">
                      <Link
                        href={`/${category.slug}`}
                        className="text-purple-400 hover:text-purple-300 transition-colors"
                      >
                        See More
                      </Link>
                    </div>
                  )}
                </div>
              )}

              {/* Comments section - same width as game content */}
              <div className="mb-12">
                <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl border-2 border-white p-6 shadow-lg">
                  <h3 className="text-xl font-bold mb-4 text-white bg-gradient-to-r from-pink-400 to-purple-400 text-transparent bg-clip-text">
                    Comments
                  </h3>
                  {/* Multiple layers of protection to prevent search engines from indexing comment content */}
                  <div
                    data-nosnippet
                    data-noindex
                    data-nofollow
                    role="complementary"
                    aria-label="User comments section"
                  >
                    {/* Additional robots meta directive for this section */}
                    <div style={{ display: 'none' }}>
                      {/* Hidden robots directive specifically for this section */}
                      <meta name="robots" content="noindex, nofollow, nosnippet, noarchive" />
                    </div>
                    <TwikooComments
                      envId="https://mytwikoo.free-gamesonline.com"
                      path={`/${categorySlug}/${gameSlug}`}
                      className="twikoo-dark-theme"
                    />
                  </div>
                </div>
              </div>

              {/* Markdown content - same width as game content */}
              {game.markdown_content && (
                <ClientSideMarkdown content={game.markdown_content} />
              )}
            </div>

            {/* Right side trending games area - vertical layout (desktop only) */}
            {trendingGames.length > 0 && (
              <div className="hidden lg:block lg:w-1/6">
                <div className="sticky top-4">
                  <h2 className="text-lg font-bold mb-3 bg-gradient-to-r from-pink-400 to-purple-400 text-transparent bg-clip-text">
                    Trending Games
                  </h2>
                  <div className="grid grid-cols-1 gap-3">
                    {trendingGames.map((trendingGame) => (
                      <Link
                        key={trendingGame.id}
                        href={`/${category?.slug || 'sprunki'}/${trendingGame.slug}`}
                        className="block bg-gray-900 rounded-lg overflow-hidden border border-purple-600/30 hover:border-purple-600 transition-all transform hover:scale-105"
                      >
                        <div className="aspect-square relative">
                          <Image
                            src={trendingGame.cover_image}
                            alt={trendingGame.title}
                            fill
                            className="object-cover"
                            sizes="100px"
                          />
                        </div>
                        <div className="p-1 text-center">
                          <h3 className="text-xs font-medium text-white hover:text-purple-400 transition-colors line-clamp-1">
                            {trendingGame.title}
                          </h3>
                        </div>
                      </Link>
                    ))}
                  </div>
                  {category && (
                    <div className="mt-3 text-center">
                      <Link
                        href={`/${category.slug}`}
                        className="text-xs text-purple-400 hover:text-purple-300 transition-colors"
                      >
                        See More
                      </Link>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
        </main>
      </>
    );
  } catch (error) {
    console.error('Error loading game page:', error);
    notFound();
  }
} 