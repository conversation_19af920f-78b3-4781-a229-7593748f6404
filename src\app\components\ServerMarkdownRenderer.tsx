import React from 'react';

interface ServerMarkdownRendererProps {
  content: string;
}

// Server-side markdown parser that works with Edge Runtime
function parseMarkdownToHTML(content: string): string {
  if (!content) return '';

  // Escape HTML special characters to prevent XSS attacks
  function escapeHtml(text: string): string {
    return text
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&quot;")
      .replace(/'/g, "&#039;");
  }

  // Preprocess content, escape HTML special characters first
  const safeContent = escapeHtml(content);

  // Split content into lines for better processing
  const lines = safeContent.split('\n');
  const processedLines: string[] = [];
  let inCodeBlock = false;
  let inList = false;
  let listType = '';

  for (let i = 0; i < lines.length; i++) {
    let line = lines[i];

    // Handle code blocks
    if (line.trim().startsWith('```')) {
      if (!inCodeBlock) {
        inCodeBlock = true;
        const language = line.trim().substring(3).trim();
        processedLines.push(`<pre class="bg-gray-800 p-4 rounded-lg overflow-x-auto mb-4 border border-gray-700"><code class="text-pink-300 text-sm language-${language}">`);
      } else {
        inCodeBlock = false;
        processedLines.push('</code></pre>');
      }
      continue;
    }

    // If we're in a code block, don't process markdown
    if (inCodeBlock) {
      processedLines.push(line);
      continue;
    }

    // Handle headings
    if (line.match(/^#{1,6} /)) {
      const level = line.match(/^#+/)?.[0].length || 1;
      const text = line.replace(/^#+\s*/, '');
      const headingClasses = {
        1: 'text-2xl font-bold mb-4 text-purple-300 mt-8',
        2: 'text-xl font-bold mb-4 text-purple-300 mt-8',
        3: 'text-lg font-bold mb-3 text-purple-300 mt-6',
        4: 'text-base font-bold mb-3 text-purple-300 mt-4',
        5: 'text-sm font-bold mb-2 text-purple-300 mt-4',
        6: 'text-xs font-bold mb-2 text-purple-300 mt-4'
      };
      processedLines.push(`<h${level} class="${headingClasses[level as keyof typeof headingClasses]}">${text}</h${level}>`);
      continue;
    }

    // Handle horizontal rules
    if (line.trim() === '---' || line.trim() === '***') {
      processedLines.push('<hr class="border-gray-600 my-6" />');
      continue;
    }

    // Handle blockquotes
    if (line.trim().startsWith('> ')) {
      const text = line.replace(/^>\s*/, '');
      processedLines.push(`<blockquote class="border-l-4 border-purple-500 pl-4 italic text-gray-300 my-4">${text}</blockquote>`);
      continue;
    }

    // Handle lists
    const unorderedMatch = line.match(/^(\s*)[*-]\s+(.*)$/);
    const orderedMatch = line.match(/^(\s*)\d+\.\s+(.*)$/);

    if (unorderedMatch || orderedMatch) {
      const isOrdered = !!orderedMatch;
      const text = (unorderedMatch || orderedMatch)?.[2] || '';
      const indent = (unorderedMatch || orderedMatch)?.[1]?.length || 0;

      if (!inList) {
        inList = true;
        listType = isOrdered ? 'ol' : 'ul';
        const listClass = isOrdered ? 'list-decimal ml-6 mb-4' : 'list-disc ml-6 mb-4';
        processedLines.push(`<${listType} class="${listClass}">`);
      } else if ((listType === 'ol') !== isOrdered) {
        // List type changed, close previous and open new
        processedLines.push(`</${listType}>`);
        listType = isOrdered ? 'ol' : 'ul';
        const listClass = isOrdered ? 'list-decimal ml-6 mb-4' : 'list-disc ml-6 mb-4';
        processedLines.push(`<${listType} class="${listClass}">`);
      }

      processedLines.push(`<li class="mb-2 text-gray-300">${text}</li>`);
      continue;
    } else if (inList && line.trim() === '') {
      // Empty line in list, continue list
      continue;
    } else if (inList) {
      // End of list
      processedLines.push(`</${listType}>`);
      inList = false;
      listType = '';
    }

    // Handle empty lines (paragraph breaks)
    if (line.trim() === '') {
      if (processedLines.length > 0 && !processedLines[processedLines.length - 1].includes('</p>')) {
        processedLines.push('</p>');
      }
      continue;
    }

    // Regular paragraph text
    if (!line.match(/^<[^>]+>/)) {
      if (processedLines.length === 0 || processedLines[processedLines.length - 1].includes('</p>') ||
          processedLines[processedLines.length - 1].includes('</h') ||
          processedLines[processedLines.length - 1].includes('</blockquote>') ||
          processedLines[processedLines.length - 1].includes('</pre>')) {
        processedLines.push(`<p class="mb-4 text-gray-300 leading-relaxed">${line}`);
      } else {
        processedLines[processedLines.length - 1] += `<br />${line}`;
      }
    } else {
      processedLines.push(line);
    }
  }

  // Close any remaining open tags
  if (inList) {
    processedLines.push(`</${listType}>`);
  }
  if (processedLines.length > 0 && !processedLines[processedLines.length - 1].includes('</p>') &&
      !processedLines[processedLines.length - 1].includes('</h') &&
      !processedLines[processedLines.length - 1].includes('</blockquote>') &&
      !processedLines[processedLines.length - 1].includes('</pre>')) {
    processedLines.push('</p>');
  }

  let html = processedLines.join('\n');

  // Apply inline formatting
  html = html
    // Bold text
    .replace(/\*\*(.*?)\*\*/g, '<strong class="text-white font-bold">$1</strong>')
    // Italic text
    .replace(/\*(.*?)\*/g, '<em class="text-gray-300 italic">$1</em>')
    // Links with proper SEO attributes
    .replace(/\[([^\]]*)\]\(([^\)]*)\)/g, '<a href="$2" class="text-purple-400 hover:text-purple-300 underline transition-colors" target="_blank" rel="noopener noreferrer">$1</a>')
    // Inline code
    .replace(/`([^`]*)`/g, '<code class="text-pink-300 bg-gray-800/50 px-2 py-1 rounded text-sm">$1</code>');

  return html;
}

// Server-side component for rendering markdown content
export default function ServerMarkdownRenderer({ content }: ServerMarkdownRendererProps) {
  if (!content) return null;
  
  // Parse markdown on the server side
  const htmlContent = parseMarkdownToHTML(content);
  
  return (
    <section className="mb-12" aria-label="Game Information">
      <div className="bg-gray-900/60 backdrop-blur-sm rounded-xl border border-purple-600/30 p-8 shadow-lg">
        <header className="mb-6">
          <h2 className="text-2xl font-bold bg-gradient-to-r from-pink-400 to-purple-400 text-transparent bg-clip-text">
            Game Information
          </h2>
        </header>
        <article 
          className="prose prose-invert max-w-none"
          dangerouslySetInnerHTML={{ __html: htmlContent }}
        />
      </div>
    </section>
  );
}
