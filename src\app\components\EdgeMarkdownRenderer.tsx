'use client';

import React from 'react';

interface EdgeMarkdownRendererProps {
  content: string;
}

// Simple markdown parser, compatible with Edge Runtime and server-side rendering
function parseMarkdown(content: string): string {
  if (!content) return '';
  
  // First escape HTML special characters to prevent XSS attacks
  function escapeHtml(text: string): string {
    return text
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&quot;")
      .replace(/'/g, "&#039;");
  }
  
  // Preprocess content, escape HTML special characters first
  const safeContent = escapeHtml(content);
  
  // Parse method that doesn't depend on document object or browser-specific APIs
  let html = safeContent
    // Headings
    .replace(/^### (.*$)/gim, '<h3 class="text-lg font-bold mb-3 text-purple-300">$1</h3>')
    .replace(/^## (.*$)/gim, '<h2 class="text-xl font-bold mb-4 text-purple-300">$1</h2>')
    .replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold mb-4 text-purple-300">$1</h1>')
    
    // Bold
    .replace(/\*\*(.*)\*\*/gim, '<strong class="text-white font-bold">$1</strong>')
    
    // Italic
    .replace(/\*(.*)\*/gim, '<em class="text-gray-300 italic">$1</em>')
    
    // Links - don't use complex character reference decoding
    .replace(/\[([^\]]*)\]\(([^\)]*)\)/gim, '<a href="$2" class="text-purple-400 hover:text-purple-300 underline" target="_blank" rel="noopener noreferrer">$1</a>')
    
    // Code blocks
    .replace(/```([^`]*)```/gim, '<pre class="bg-gray-800 p-4 rounded-lg overflow-x-auto mb-4"><code class="text-pink-300">$1</code></pre>')
    
    // Inline code
    .replace(/`([^`]*)`/gim, '<code class="text-pink-300 bg-gray-800/50 px-2 py-1 rounded">$1</code>')
    
    // Unordered lists
    .replace(/^\* (.*$)/gim, '<li class="ml-4 mb-1">• $1</li>')
    .replace(/^- (.*$)/gim, '<li class="ml-4 mb-1">• $1</li>')
    
    // Ordered lists
    .replace(/^\d+\. (.*$)/gim, '<li class="ml-4 mb-1 list-decimal">$1</li>')
    
    // Paragraphs (line breaks)
    .replace(/\n\n/gim, '</p><p class="mb-4 text-gray-300">')
    .replace(/\n/gim, '<br />');
  
  // Wrap paragraphs
  if (html && !html.startsWith('<')) {
    html = '<p class="mb-4 text-gray-300">' + html + '</p>';
  }
  
  return html;
}

// Pure client-side component, ensuring it works well in Edge environment
export default function EdgeMarkdownRenderer({ content }: EdgeMarkdownRendererProps) {
  // Ensure Markdown is parsed during component rendering, not at module level
  const htmlContent = React.useMemo(() => parseMarkdown(content), [content]);
  
  return (
    <div 
      className="prose prose-invert max-w-none"
      dangerouslySetInnerHTML={{ __html: htmlContent }}
    />
  );
}